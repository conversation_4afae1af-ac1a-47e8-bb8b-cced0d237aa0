{"name": "admin-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "bun --bun next dev -H 0.0.0.0", "build": "bun --bun next build", "start": "bun --bun next start -H 0.0.0.0", "lint": "bun --bun next lint", "type-check": "tsc --noEmit", "create-user": "bun --bun scripts/create-user.ts"}, "dependencies": {"bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.509.0", "next": "15.3.2", "next-auth": "^4.24.11", "pg": "^8.15.6", "react": "^19.0.0", "react-dom": "^19.0.0", "swr": "^2.3.3", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}