'use client';

import { useState } from 'react';
import { RefreshCw, Search, Upload, Trash2, Edit2 } from 'lucide-react';
import useSWR from 'swr';

// 定义监控地址类型
interface MonitoredAddress {
  address: string;
  label: string | null;
  created_at: string;
}

// 获取数据的 fetcher 函数
const fetcher = (url: string) => fetch(url).then((res) => res.json());

export default function MonitoredAddressesPage() {
  // 状态管理
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [importText, setImportText] = useState('');
  const [showImportModal, setShowImportModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [currentAddress, setCurrentAddress] = useState<MonitoredAddress | null>(null);
  const [newLabel, setNewLabel] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 构建 API URL
  const apiUrl = `/api/wallet/monitored?page=${page}&search=${searchQuery}`;

  // 使用 SWR 获取数据
  const { data, error, isLoading, mutate } = useSWR(apiUrl, fetcher);

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    mutate();
  };

  // 导入错误信息
  const [importError, setImportError] = useState<string | null>(null);
  const [duplicateLabels, setDuplicateLabels] = useState<string[]>([]);

  // 处理导入
  const handleImport = async () => {
    if (!importText.trim()) {
      return;
    }

    try {
      setIsSubmitting(true);
      setImportError(null);
      setDuplicateLabels([]);

      const response = await fetch('/api/wallet/monitored', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ addresses: importText }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (data.error === '存在重复的标签' || data.error === '数据库中已存在相同的标签') {
          setImportError(data.error);
          setDuplicateLabels(data.duplicateLabels || []);
          return;
        }
        throw new Error(data.error || '导入失败');
      }

      // 重置状态并刷新数据
      setImportText('');
      setShowImportModal(false);
      mutate();
    } catch (error) {
      console.error('导入错误:', error);
      setImportError(error instanceof Error ? error.message : '导入失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理删除
  const handleDelete = async (address: string) => {
    if (!confirm('确定要删除此监控地址吗？')) {
      return;
    }

    try {
      const response = await fetch(`/api/wallet/monitored/${address}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('删除失败');
      }

      mutate();
    } catch (error) {
      console.error('删除错误:', error);
      alert('删除失败，请稍后重试');
    }
  };

  // 处理编辑
  const handleEdit = (address: MonitoredAddress) => {
    setCurrentAddress(address);
    setNewLabel(address.label || '');
    setShowEditModal(true);
  };

  // 编辑错误信息
  const [editError, setEditError] = useState<string | null>(null);

  // 提交编辑
  const handleSubmitEdit = async () => {
    if (!currentAddress) return;

    try {
      setIsSubmitting(true);
      setEditError(null);

      const response = await fetch(`/api/wallet/monitored/${currentAddress.address}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ label: newLabel }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (data.error === '已存在相同的标签') {
          setEditError(`标签 "${newLabel}" 已被其他地址使用`);
          return;
        }
        throw new Error(data.error || '更新失败');
      }

      // 重置状态并刷新数据
      setShowEditModal(false);
      setCurrentAddress(null);
      setNewLabel('');
      mutate();
    } catch (error) {
      console.error('更新错误:', error);
      setEditError(error instanceof Error ? error.message : '更新失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 格式化地址显示
  const formatAddress = (address: string) => {
    if (address.length <= 12) return address;
    return `${address.substring(0, 6)}...${address.substring(address.length - 6)}`;
  };

  // 将 UTC 时间转换为北京时间 (UTC+8)
  const formatToBeijingTime = (utcTimeString: string) => {
    if (!utcTimeString) return '-';

    try {
      const date = new Date(utcTimeString);

      // 检查日期是否有效
      if (isNaN(date.getTime())) return '-';

      // 获取北京时间字符串 (UTC+8)
      return new Date(date.getTime() + 8 * 60 * 60 * 1000)
        .toISOString()
        .replace('T', ' ')
        .substring(0, 19) + ' (北京时间)';
    } catch (error) {
      console.error('时间格式化错误:', error);
      return '-';
    }
  };

  // 复制到剪贴板
  const [copySuccess, setCopySuccess] = useState<string | null>(null);

  const copyToClipboard = (text: string) => {
    try {
      // 创建临时文本区域
      const textArea = document.createElement('textarea');
      textArea.value = text;

      // 确保文本区域不可见
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);

      // 选择文本并复制
      textArea.focus();
      textArea.select();
      document.execCommand('copy');

      // 移除临时元素
      document.body.removeChild(textArea);

      // 显示成功提示
      setCopySuccess(text);

      // 3秒后自动清除提示
      setTimeout(() => {
        setCopySuccess(null);
      }, 3000);
    } catch (err) {
      console.error('复制失败:', err);
      setCopySuccess('复制失败');
      setTimeout(() => {
        setCopySuccess(null);
      }, 3000);
    }
  };

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">钱包监控列表</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => mutate()}
            className="flex items-center rounded-md bg-blue-50 px-3 py-2 text-sm text-blue-600 hover:bg-blue-100"
            disabled={isLoading}
          >
            <RefreshCw className={`mr-1 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            刷新
          </button>
          <button
            onClick={() => setShowImportModal(true)}
            className="flex items-center rounded-md bg-green-50 px-3 py-2 text-sm text-green-600 hover:bg-green-100"
          >
            <Upload className="mr-1 h-4 w-4" />
            导入地址
          </button>
        </div>
      </div>

      <div className="mb-6 rounded-lg bg-white p-4 shadow-sm">
        <form onSubmit={handleSearch} className="flex">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="搜索地址或备注..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full rounded-md border border-gray-300 pl-10 pr-4 py-2"
            />
          </div>
          <button
            type="submit"
            className="ml-2 rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            搜索
          </button>
        </form>
      </div>

      {isLoading ? (
        <div className="flex h-64 items-center justify-center rounded-lg bg-white">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
        </div>
      ) : error ? (
        <div className="flex h-64 items-center justify-center rounded-lg bg-white">
          <p className="text-red-500">加载失败，请重试</p>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto rounded-lg bg-white shadow-sm">
            <table className="w-full table-auto">
              <thead>
                <tr className="border-b bg-gray-50 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  <th className="px-6 py-3">地址</th>
                  <th className="px-6 py-3">标签</th>
                  <th className="px-6 py-3">添加时间</th>
                  <th className="px-6 py-3">操作</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {data && data.addresses && Array.isArray(data.addresses) ? (
                  data.addresses.map((address: MonitoredAddress) => (
                    <tr key={address.address || Math.random()} className="hover:bg-gray-50">
                      <td className="whitespace-nowrap px-6 py-4 text-sm">
                        <div className="flex items-center">
                          <a
                            href={`https://solscan.io/account/${address.address}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                            title={address.address}
                          >
                            {address.address}
                          </a>
                          <button
                            onClick={() => copyToClipboard(address.address)}
                            className="ml-1 rounded-full p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
                            title="复制地址"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900">
                        {address.label || '-'}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                        {address.created_at ? formatToBeijingTime(address.created_at) : '-'}
                      </td>
                      <td className="whitespace-nowrap px-6 py-4 text-sm">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEdit(address)}
                            className="rounded-md p-1 text-blue-600 hover:bg-blue-50"
                            title="编辑"
                          >
                            <Edit2 className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(address.address)}
                            className="rounded-md p-1 text-red-600 hover:bg-red-50"
                            title="删除"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                      没有数据
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {(!data?.addresses || data.addresses.length === 0) && (
            <div className="mt-4 rounded-lg bg-white p-8 text-center">
              <p className="text-gray-500">没有找到监控地址</p>
            </div>
          )}

          {data && data.totalPages && data.totalPages > 1 && (
            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-gray-500">
                共 {data.totalRecords || 0} 条记录，第 {page} / {data.totalPages} 页
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="rounded-md border px-3 py-1 text-sm disabled:opacity-50"
                >
                  上一页
                </button>
                <button
                  onClick={() => setPage(Math.min(data.totalPages, page + 1))}
                  disabled={page === data.totalPages}
                  className="rounded-md border px-3 py-1 text-sm disabled:opacity-50"
                >
                  下一页
                </button>
              </div>
            </div>
          )}
        </>
      )}

      {/* 导入地址模态框 */}
      {showImportModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
            <h2 className="mb-4 text-xl font-bold">导入监控地址</h2>
            <p className="mb-4 text-sm text-gray-600">
              每行输入一个地址，可选添加备注（用逗号分隔）。<br />
              例如：<br />
              7rhxnLV8C77o6d8oz26AgK8x8m5ePsdeRawjqvojbjnQ<br />
              7rhxnLV8C77o6d8oz26AgK8x8m5ePsdeRawjqvojbjnQ,币安
            </p>

            {importError && (
              <div className="mb-4 rounded-md bg-red-50 p-4 text-sm text-red-700">
                <p className="font-medium">{importError}</p>
                {duplicateLabels.length > 0 && (
                  <ul className="mt-2 list-inside list-disc">
                    {duplicateLabels.map((label, index) => (
                      <li key={index}>{label}</li>
                    ))}
                  </ul>
                )}
              </div>
            )}

            <textarea
              value={importText}
              onChange={(e) => setImportText(e.target.value)}
              className="mb-4 h-40 w-full rounded-md border border-gray-300 p-2"
              placeholder="输入地址，每行一个..."
            />
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  setShowImportModal(false);
                  setImportError(null);
                  setDuplicateLabels([]);
                }}
                className="rounded-md border px-4 py-2 text-sm"
                disabled={isSubmitting}
              >
                取消
              </button>
              <button
                onClick={handleImport}
                className="rounded-md bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700 disabled:bg-blue-400"
                disabled={isSubmitting || !importText.trim()}
              >
                {isSubmitting ? '导入中...' : '导入'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑标签模态框 */}
      {showEditModal && currentAddress && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="w-full max-w-md rounded-lg bg-white p-6 shadow-xl">
            <h2 className="mb-4 text-xl font-bold">编辑地址标签</h2>

            {editError && (
              <div className="mb-4 rounded-md bg-red-50 p-4 text-sm text-red-700">
                <p>{editError}</p>
              </div>
            )}

            <div className="mb-4">
              <label className="mb-1 block text-sm font-medium">地址</label>
              <input
                type="text"
                value={currentAddress.address}
                readOnly
                className="w-full rounded-md border border-gray-300 bg-gray-50 p-2"
              />
            </div>
            <div className="mb-4">
              <label className="mb-1 block text-sm font-medium">标签</label>
              <input
                type="text"
                value={newLabel}
                onChange={(e) => setNewLabel(e.target.value)}
                className="w-full rounded-md border border-gray-300 p-2"
                placeholder="输入标签..."
              />
            </div>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setEditError(null);
                }}
                className="rounded-md border px-4 py-2 text-sm"
                disabled={isSubmitting}
              >
                取消
              </button>
              <button
                onClick={handleSubmitEdit}
                className="rounded-md bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700 disabled:bg-blue-400"
                disabled={isSubmitting}
              >
                {isSubmitting ? '保存中...' : '保存'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 复制成功提示 */}
      {copySuccess && (
        <div className="fixed bottom-4 right-4 z-50 rounded-md bg-green-100 px-4 py-2 text-sm text-green-800 shadow-lg">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="mr-2 h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>
              {copySuccess === '复制失败' ? '复制失败，请手动复制' : '已复制到剪贴板'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
