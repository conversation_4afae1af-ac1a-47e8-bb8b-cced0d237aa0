'use client';

import { useEffect, useState } from 'react';
import { Wallet, Users } from 'lucide-react';
import Link from 'next/link';

export default function DashboardPage() {
  const [stats, setStats] = useState({
    totalTransfers: 0,
    totalMonitored: 0,
    todayTransfers: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchStats() {
      try {
        const response = await fetch('/api/dashboard/stats');
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchStats();
  }, []);

  return (
    <div>
      <h1 className="mb-6 text-2xl font-bold">仪表盘</h1>
      
      {loading ? (
        <div className="flex justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <StatCard
            title="今日交易记录"
            value={stats.todayTransfers}
            icon={Wallet}
            href="/wallet/transfers"
            color="bg-blue-500"
          />
          <StatCard
            title="总交易记录"
            value={stats.totalTransfers}
            icon={Wallet}
            href="/wallet/transfers"
            color="bg-green-500"
          />
          <StatCard
            title="监控地址数量"
            value={stats.totalMonitored}
            icon={Users}
            href="/wallet/monitored"
            color="bg-purple-500"
          />
        </div>
      )}
      
      <div className="mt-8">
        <h2 className="mb-4 text-xl font-semibold">快速导航</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <Link
            href="/wallet/transfers"
            className="flex items-center rounded-lg border p-4 hover:bg-gray-50"
          >
            <div className="mr-4 rounded-full bg-blue-100 p-3 text-blue-600">
              <Wallet className="h-6 w-6" />
            </div>
            <div>
              <h3 className="font-medium">钱包追踪</h3>
              <p className="text-sm text-gray-600">查看和导出钱包交易记录</p>
            </div>
          </Link>
          
          <Link
            href="/wallet/monitored"
            className="flex items-center rounded-lg border p-4 hover:bg-gray-50"
          >
            <div className="mr-4 rounded-full bg-purple-100 p-3 text-purple-600">
              <Users className="h-6 w-6" />
            </div>
            <div>
              <h3 className="font-medium">钱包监控列表</h3>
              <p className="text-sm text-gray-600">管理监控的钱包地址</p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}

function StatCard({ 
  title, 
  value, 
  icon: Icon, 
  href, 
  color 
}: { 
  title: string; 
  value: number; 
  icon: any; 
  href: string; 
  color: string;
}) {
  return (
    <Link href={href}>
      <div className="rounded-lg bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
        <div className="flex items-center">
          <div className={`rounded-full ${color} p-3 text-white`}>
            <Icon className="h-6 w-6" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium">{title}</h3>
            <p className="text-2xl font-bold">{value.toLocaleString()}</p>
          </div>
        </div>
      </div>
    </Link>
  );
}
