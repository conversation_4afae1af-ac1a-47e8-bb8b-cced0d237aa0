import { NextResponse } from 'next/server';
import pool from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET() {
  try {
    // 检查用户是否已认证
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 获取总交易记录数
    const totalTransfersResult = await pool.query(
      'SELECT COUNT(*) FROM wallet_transfer_logs'
    );
    const totalTransfers = parseInt(totalTransfersResult.rows[0].count);

    // 获取今日交易记录数
    const todayTransfersResult = await pool.query(
      `SELECT COUNT(*) FROM wallet_transfer_logs
       WHERE timestamp >= CURRENT_DATE AND timestamp < CURRENT_DATE + INTERVAL '1 day'`
    );
    const todayTransfers = parseInt(todayTransfersResult.rows[0].count);

    // 获取监控地址数量
    const totalMonitoredResult = await pool.query(
      'SELECT COUNT(*) FROM wallet_monitored_addresses'
    );
    const totalMonitored = parseInt(totalMonitoredResult.rows[0].count);

    return NextResponse.json({
      totalTransfers,
      todayTransfers,
      totalMonitored,
    });
  } catch (error) {
    console.error('获取统计数据失败:', error);
    return NextResponse.json(
      { error: '获取统计数据失败' },
      { status: 500 }
    );
  }
}
