import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // 检查用户是否已认证
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search') || '';

    const offset = (page - 1) * limit;

    let query = `
      SELECT * FROM wallet_monitored_addresses
      WHERE 1=1
    `;

    let countQuery = `
      SELECT COUNT(*) FROM wallet_monitored_addresses
      WHERE 1=1
    `;

    const queryParams: any[] = [];
    const countQueryParams: any[] = [];

    // 添加搜索条件
    if (search) {
      query += ` AND (address ILIKE $1 OR COALESCE(label, '') ILIKE $1)`;
      countQuery += ` AND (address ILIKE $1 OR COALESCE(label, '') ILIKE $1)`;
      queryParams.push(`%${search}%`);
      countQueryParams.push(`%${search}%`);
    }

    // 添加排序和分页
    query += ` ORDER BY created_at DESC LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
    queryParams.push(limit, offset);

    // 执行查询
    const result = await pool.query(query, queryParams);
    const countResult = await pool.query(countQuery, countQueryParams);

    const totalRecords = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalRecords / limit);

    return NextResponse.json({
      addresses: result.rows,
      page,
      limit,
      totalRecords,
      totalPages,
    });
  } catch (error) {
    console.error('获取监控地址失败:', error);
    return NextResponse.json(
      { error: '获取监控地址失败' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // 检查用户是否已认证
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 获取请求体
    const body = await request.json();
    const { addresses } = body;

    if (!addresses || typeof addresses !== 'string') {
      return NextResponse.json(
        { error: '无效的地址格式' },
        { status: 400 }
      );
    }

    // 解析地址列表
    const addressLines = addresses.split('\n').filter(line => line.trim());

    // 批量插入地址
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      let inserted = 0;
      let skipped = 0;

      // 首先检查是否有重复的标签
      const labels = new Map();
      const duplicateLabels = [];

      for (const line of addressLines) {
        const parts = line.trim().split(',');
        const address = parts[0].trim();
        const label = parts.length > 1 ? parts[1].trim() : null;

        // 如果有标签，检查是否重复
        if (label) {
          if (labels.has(label)) {
            duplicateLabels.push(label);
          } else {
            labels.set(label, address);
          }
        }
      }

      // 如果有重复的标签，返回错误
      if (duplicateLabels.length > 0) {
        return NextResponse.json(
          {
            error: '存在重复的标签',
            duplicateLabels
          },
          { status: 400 }
        );
      }

      // 检查数据库中是否已存在相同的标签
      for (const [label, _] of labels.entries()) {
        if (label) {
          const existingLabel = await client.query(
            'SELECT address FROM wallet_monitored_addresses WHERE label = $1',
            [label]
          );

          if (existingLabel.rows.length > 0) {
            return NextResponse.json(
              {
                error: '数据库中已存在相同的标签',
                duplicateLabels: [label]
              },
              { status: 400 }
            );
          }
        }
      }

      // 如果没有重复的标签，继续插入数据
      for (const line of addressLines) {
        const parts = line.trim().split(',');
        const address = parts[0].trim();
        const label = parts.length > 1 ? parts[1].trim() : null;

        // 验证地址格式（简单验证，Solana 地址通常是 base58 编码的 32-44 字符）
        if (!address || address.length < 32 || address.length > 44) {
          continue;
        }

        try {
          const result = await client.query(
            `INSERT INTO wallet_monitored_addresses (address, label)
             VALUES ($1, $2)
             ON CONFLICT (address)
             DO UPDATE SET label = EXCLUDED.label
             RETURNING *`,
            [address, label]
          );

          if (result.rowCount > 0) {
            inserted++;
          } else {
            skipped++;
          }
        } catch (err) {
          console.error('插入地址失败:', err);
          skipped++;
        }
      }

      await client.query('COMMIT');

      return NextResponse.json({
        success: true,
        inserted,
        skipped,
        total: addressLines.length,
      });
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('导入监控地址失败:', error);
    return NextResponse.json(
      { error: '导入监控地址失败' },
      { status: 500 }
    );
  }
}
