import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { address: string } }
) {
  try {
    // 检查用户是否已认证
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const { address } = params;

    if (!address) {
      return NextResponse.json(
        { error: '地址参数缺失' },
        { status: 400 }
      );
    }

    // 删除地址
    const result = await pool.query(
      'DELETE FROM wallet_monitored_addresses WHERE address = $1 RETURNING *',
      [address]
    );

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: '地址不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '地址已删除',
    });
  } catch (error) {
    console.error('删除监控地址失败:', error);
    return NextResponse.json(
      { error: '删除监控地址失败' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { address: string } }
) {
  try {
    // 检查用户是否已认证
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const { address } = params;

    if (!address) {
      return NextResponse.json(
        { error: '地址参数缺失' },
        { status: 400 }
      );
    }

    // 获取请求体
    const body = await request.json();
    const { label } = body;

    // 如果有标签，检查是否已存在相同的标签
    if (label && label.trim() !== '') {
      const existingLabel = await pool.query(
        'SELECT address FROM wallet_monitored_addresses WHERE label = $1 AND address != $2',
        [label, address]
      );

      if (existingLabel.rows.length > 0) {
        return NextResponse.json(
          {
            error: '已存在相同的标签',
            existingAddress: existingLabel.rows[0].address
          },
          { status: 400 }
        );
      }
    }

    // 更新地址标签
    const result = await pool.query(
      'UPDATE wallet_monitored_addresses SET label = $1 WHERE address = $2 RETURNING *',
      [label, address]
    );

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: '地址不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '标签已更新',
      address: result.rows[0],
    });
  } catch (error) {
    console.error('更新监控地址标签失败:', error);
    return NextResponse.json(
      { error: '更新监控地址标签失败' },
      { status: 500 }
    );
  }
}
