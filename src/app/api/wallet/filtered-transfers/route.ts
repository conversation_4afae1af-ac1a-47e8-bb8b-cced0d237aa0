import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // 检查用户是否已认证
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const startDate = searchParams.get('startDate') || new Date().toISOString().split('T')[0];
    const endDate = searchParams.get('endDate') || new Date().toISOString().split('T')[0];
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const monitoredOnly = searchParams.get('monitoredOnly') === 'true';
    const search = searchParams.get('search') || '';

    // 金额过滤参数
    const minAmount = parseFloat(searchParams.get('minAmount') || '0.1');
    const maxAmount = parseFloat(searchParams.get('maxAmount') || '50');

    // 验证金额范围
    const validMinAmount = Math.max(0.1, Math.min(minAmount, 50));
    const validMaxAmount = Math.max(validMinAmount, Math.min(maxAmount, 50));

    const offset = (page - 1) * limit;

    // 构建查询
    let query = `
      SELECT ft.*,
             ma1.label as from_label,
             ma2.label as to_label
      FROM wallet_filtered_transfers ft
      LEFT JOIN wallet_monitored_addresses ma1 ON ft.from_address = ma1.address
      LEFT JOIN wallet_monitored_addresses ma2 ON ft.to_address = ma2.address
      WHERE ft.timestamp::date >= $1::date
      AND ft.timestamp::date <= $2::date
    `;

    let countQuery = `
      SELECT COUNT(*)
      FROM wallet_filtered_transfers ft
      LEFT JOIN wallet_monitored_addresses ma1 ON ft.from_address = ma1.address
      LEFT JOIN wallet_monitored_addresses ma2 ON ft.to_address = ma2.address
      WHERE ft.timestamp::date >= $1::date
      AND ft.timestamp::date <= $2::date
    `;

    let queryParams = [startDate, endDate];
    let countQueryParams = [startDate, endDate];

    // 添加搜索条件
    if (search) {
      query += ` AND (
        ft.from_address ILIKE $${queryParams.length + 1}
        OR ft.to_address ILIKE $${queryParams.length + 1}
        OR ma1.label ILIKE $${queryParams.length + 2}
        OR ma2.label ILIKE $${queryParams.length + 2}
      )`;
      countQuery += ` AND (
        ft.from_address ILIKE $${countQueryParams.length + 1}
        OR ft.to_address ILIKE $${countQueryParams.length + 1}
        OR ma1.label ILIKE $${countQueryParams.length + 2}
        OR ma2.label ILIKE $${countQueryParams.length + 2}
      )`;
      queryParams.push(`%${search}%`, `%${search}%`);
      countQueryParams.push(`%${search}%`, `%${search}%`);
    }

    // 添加监控地址过滤
    if (monitoredOnly) {
      query += ` AND ft.is_monitored = true`;
      countQuery += ` AND ft.is_monitored = true`;
    }

    // 添加金额过滤
    query += ` AND ft.amount >= $${queryParams.length + 1} AND ft.amount <= $${queryParams.length + 2}`;
    countQuery += ` AND ft.amount >= $${countQueryParams.length + 1} AND ft.amount <= $${countQueryParams.length + 2}`;
    queryParams.push(validMinAmount.toString(), validMaxAmount.toString());
    countQueryParams.push(validMinAmount.toString(), validMaxAmount.toString());

    // 添加排序和分页
    query += ` ORDER BY ft.timestamp DESC LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
    queryParams.push(limit.toString(), offset.toString());

    // 执行查询
    const result = await pool.query(query, queryParams);
    const countResult = await pool.query(countQuery, countQueryParams);

    const totalRecords = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalRecords / limit);

    return NextResponse.json({
      transfers: result.rows,
      page,
      limit,
      totalRecords,
      totalPages,
    });
  } catch (error) {
    console.error('获取过滤交易记录失败:', error);
    return NextResponse.json(
      { error: '获取过滤交易记录失败' },
      { status: 500 }
    );
  }
}
