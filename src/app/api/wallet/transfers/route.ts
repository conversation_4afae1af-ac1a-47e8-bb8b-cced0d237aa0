import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // 检查用户是否已认证
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const filterType = searchParams.get('filterType') || 'first';
    const startDate = searchParams.get('startDate') || new Date().toISOString().split('T')[0];
    const endDate = searchParams.get('endDate') || new Date().toISOString().split('T')[0];
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const monitoredOnly = searchParams.get('monitoredOnly') === 'true';
    const search = searchParams.get('search') || '';

    // 金额过滤参数
    const minAmount = parseFloat(searchParams.get('minAmount') || '0.1');
    const maxAmount = parseFloat(searchParams.get('maxAmount') || '50');

    // 验证金额范围
    const validMinAmount = Math.max(0.1, Math.min(minAmount, 50));
    const validMaxAmount = Math.max(validMinAmount, Math.min(maxAmount, 50));

    const offset = (page - 1) * limit;

    let query = '';
    let countQuery = '';
    let queryParams: any[] = [];
    let countQueryParams: any[] = [];

    if (filterType === 'first') {
      // 第一次过滤：wallet_transfer_logs 表
      query = `
        SELECT tl.*,
               ma1.label as from_label,
               ma2.label as to_label,
               ma1.address as from_monitored_address,
               ma2.address as to_monitored_address
        FROM wallet_transfer_logs tl
        LEFT JOIN wallet_monitored_addresses ma1 ON tl.from_address = ma1.address
        LEFT JOIN wallet_monitored_addresses ma2 ON tl.to_address = ma2.address
        WHERE tl.timestamp::date >= $1::date
        AND tl.timestamp::date <= $2::date
      `;

      countQuery = `
        SELECT COUNT(*)
        FROM wallet_transfer_logs tl
        ${monitoredOnly ? `
          LEFT JOIN wallet_monitored_addresses ma1 ON tl.from_address = ma1.address
          LEFT JOIN wallet_monitored_addresses ma2 ON tl.to_address = ma2.address
        ` : ''}
        WHERE tl.timestamp::date >= $1::date
        AND tl.timestamp::date <= $2::date
      `;

      queryParams = [startDate, endDate];
      countQueryParams = [startDate, endDate];

      // 添加搜索条件
      if (search) {
        query += ` AND (
          tl.from_address ILIKE $${queryParams.length + 1}
          OR tl.to_address ILIKE $${queryParams.length + 1}
          OR EXISTS (
            SELECT 1 FROM wallet_monitored_addresses ma
            WHERE (ma.address = tl.from_address OR ma.address = tl.to_address)
            AND ma.label ILIKE $${queryParams.length + 2}
          )
        )`;
        countQuery += ` AND (
          tl.from_address ILIKE $${countQueryParams.length + 1}
          OR tl.to_address ILIKE $${countQueryParams.length + 1}
          OR EXISTS (
            SELECT 1 FROM wallet_monitored_addresses ma
            WHERE (ma.address = tl.from_address OR ma.address = tl.to_address)
            AND ma.label ILIKE $${countQueryParams.length + 2}
          )
        )`;
        queryParams.push(`%${search}%`, `%${search}%`);
        countQueryParams.push(`%${search}%`, `%${search}%`);
      }

      // 添加监控地址过滤
      if (monitoredOnly) {
        query += ` AND (ma1.address IS NOT NULL OR ma2.address IS NOT NULL)`;
        countQuery += ` AND (ma1.address IS NOT NULL OR ma2.address IS NOT NULL)`;
      }

      // 添加金额过滤
      query += ` AND tl.amount >= $${queryParams.length + 1} AND tl.amount <= $${queryParams.length + 2}`;
      countQuery += ` AND tl.amount >= $${countQueryParams.length + 1} AND tl.amount <= $${countQueryParams.length + 2}`;
      queryParams.push(validMinAmount, validMaxAmount);
      countQueryParams.push(validMinAmount, validMaxAmount);

      query += ` ORDER BY tl.timestamp DESC LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
      queryParams.push(limit, offset);

    } else {
      // 第二次过滤：wallet_second_filter 表
      query = `
        SELECT sf.*,
               ma1.label as from_label,
               ma2.label as to_label,
               ma1.address as from_monitored_address,
               ma2.address as to_monitored_address
        FROM wallet_second_filter sf
        LEFT JOIN wallet_monitored_addresses ma1 ON sf.from_address = ma1.address
        LEFT JOIN wallet_monitored_addresses ma2 ON sf.to_address = ma2.address
        WHERE sf.timestamp::date >= $1::date
        AND sf.timestamp::date <= $2::date
        AND sf.status = 1
      `;

      countQuery = `
        SELECT COUNT(*)
        FROM wallet_second_filter sf
        LEFT JOIN wallet_monitored_addresses ma1 ON sf.from_address = ma1.address
        LEFT JOIN wallet_monitored_addresses ma2 ON sf.to_address = ma2.address
        WHERE sf.timestamp::date >= $1::date
        AND sf.timestamp::date <= $2::date
        AND sf.status = 1
      `;

      queryParams = [startDate, endDate];
      countQueryParams = [startDate, endDate];

      // 添加搜索条件
      if (search) {
        query += ` AND (
          sf.from_address ILIKE $${queryParams.length + 1}
          OR sf.to_address ILIKE $${queryParams.length + 1}
          OR EXISTS (
            SELECT 1 FROM wallet_monitored_addresses ma
            WHERE (ma.address = sf.from_address OR ma.address = sf.to_address)
            AND ma.label ILIKE $${queryParams.length + 2}
          )
        )`;
        countQuery += ` AND (
          sf.from_address ILIKE $${countQueryParams.length + 1}
          OR sf.to_address ILIKE $${countQueryParams.length + 1}
          OR EXISTS (
            SELECT 1 FROM wallet_monitored_addresses ma
            WHERE (ma.address = sf.from_address OR ma.address = sf.to_address)
            AND ma.label ILIKE $${countQueryParams.length + 2}
          )
        )`;
        queryParams.push(`%${search}%`, `%${search}%`);
        countQueryParams.push(`%${search}%`, `%${search}%`);
      }

      // 添加监控地址过滤
      if (monitoredOnly) {
        query += ` AND (ma1.address IS NOT NULL OR ma2.address IS NOT NULL)`;
        countQuery += ` AND (ma1.address IS NOT NULL OR ma2.address IS NOT NULL)`;
      }

      // 添加金额过滤
      query += ` AND sf.amount >= $${queryParams.length + 1} AND sf.amount <= $${queryParams.length + 2}`;
      countQuery += ` AND sf.amount >= $${countQueryParams.length + 1} AND sf.amount <= $${countQueryParams.length + 2}`;
      queryParams.push(validMinAmount, validMaxAmount);
      countQueryParams.push(validMinAmount, validMaxAmount);

      query += ` ORDER BY sf.timestamp DESC LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
      queryParams.push(limit, offset);
    }

    // 执行查询
    const result = await pool.query(query, queryParams);
    const countResult = await pool.query(countQuery, countQueryParams);

    const totalRecords = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalRecords / limit);

    return NextResponse.json({
      transfers: result.rows,
      page,
      limit,
      totalRecords,
      totalPages,
    });
  } catch (error) {
    console.error('获取交易记录失败:', error);
    return NextResponse.json(
      { error: '获取交易记录失败' },
      { status: 500 }
    );
  }
}
