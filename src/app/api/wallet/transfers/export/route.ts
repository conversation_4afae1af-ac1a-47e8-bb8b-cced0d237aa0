import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // 检查用户是否已认证
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const filterType = searchParams.get('filterType') || 'first';
    const startDate = searchParams.get('startDate') || new Date().toISOString().split('T')[0];
    const endDate = searchParams.get('endDate') || new Date().toISOString().split('T')[0];
    const monitoredOnly = searchParams.get('monitoredOnly') === 'true';
    const search = searchParams.get('search') || '';

    // 金额过滤参数
    const minAmount = parseFloat(searchParams.get('minAmount') || '0.1');
    const maxAmount = parseFloat(searchParams.get('maxAmount') || '50');

    // 验证金额范围
    const validMinAmount = Math.max(0.1, Math.min(minAmount, 50));
    const validMaxAmount = Math.max(validMinAmount, Math.min(maxAmount, 50));

    let query = '';
    let queryParams: any[] = [];

    if (filterType === 'first') {
      // 第一次过滤：wallet_transfer_logs 表
      query = `
        SELECT tl.id, tl.signature, tl.slot, tl.from_address, tl.to_address, tl.amount, tl.timestamp,
               ma1.label as from_label, ma2.label as to_label
        FROM wallet_transfer_logs tl
        LEFT JOIN wallet_monitored_addresses ma1 ON tl.from_address = ma1.address
        LEFT JOIN wallet_monitored_addresses ma2 ON tl.to_address = ma2.address
        WHERE tl.timestamp::date >= $1::date
        AND tl.timestamp::date <= $2::date
      `;

      queryParams = [startDate, endDate];

      // 添加搜索条件
      if (search) {
        query += ` AND (tl.from_address ILIKE $${queryParams.length + 1} OR tl.to_address ILIKE $${queryParams.length + 1})`;
        queryParams.push(`%${search}%`);
      }

      // 添加监控地址过滤
      if (monitoredOnly) {
        query += ` AND (ma1.address IS NOT NULL OR ma2.address IS NOT NULL)`;
      }

      // 添加金额过滤
      query += ` AND tl.amount >= $${queryParams.length + 1} AND tl.amount <= $${queryParams.length + 2}`;
      queryParams.push(validMinAmount, validMaxAmount);

      query += ` ORDER BY tl.timestamp DESC`;

    } else if (filterType === 'second') {
      // 第二次过滤：wallet_second_filter 表
      query = `
        SELECT sf.id, sf.signature, sf.slot, sf.from_address, sf.to_address, sf.amount, sf.timestamp, sf.status, sf.checked_at,
               ma1.label as from_label, ma2.label as to_label
        FROM wallet_second_filter sf
        LEFT JOIN wallet_monitored_addresses ma1 ON sf.from_address = ma1.address
        LEFT JOIN wallet_monitored_addresses ma2 ON sf.to_address = ma2.address
        WHERE sf.timestamp::date >= $1::date
        AND sf.timestamp::date <= $2::date
        AND sf.status = 1
      `;

      queryParams = [startDate, endDate];

      // 添加搜索条件
      if (search) {
        query += ` AND (sf.from_address ILIKE $${queryParams.length + 1} OR sf.to_address ILIKE $${queryParams.length + 1})`;
        queryParams.push(`%${search}%`);
      }

      // 添加监控地址过滤
      if (monitoredOnly) {
        query += ` AND (ma1.address IS NOT NULL OR ma2.address IS NOT NULL)`;
      }

      // 添加金额过滤
      query += ` AND sf.amount >= $${queryParams.length + 1} AND sf.amount <= $${queryParams.length + 2}`;
      queryParams.push(validMinAmount, validMaxAmount);

      query += ` ORDER BY sf.timestamp DESC`;
    }

    // 执行查询
    const result = await pool.query(query, queryParams);

    // 生成 CSV 内容
    let csvContent = '';

    if (filterType === 'first') {
      // 第一次过滤的 CSV 头
      csvContent = 'ID,签名,区块高度,发送方,发送方标签,接收方,接收方标签,金额(SOL),时间\n';

      // 添加数据行
      result.rows.forEach((row) => {
        // 使用北京时间 (UTC+8)
        const beijingTime = new Date(new Date(row.timestamp).getTime() + 8 * 60 * 60 * 1000).toLocaleString('zh-CN');
        csvContent += `${row.id},${row.signature},${row.slot},${row.from_address},${row.from_label || ''},${row.to_address},${row.to_label || ''},${row.amount},${beijingTime}\n`;
      });
    } else if (filterType === 'second') {
      // 第二次过滤的 CSV 头
      csvContent = 'ID,签名,区块高度,发送方,发送方标签,接收方,接收方标签,金额(SOL),时间,状态,检测时间\n';

      // 添加数据行
      result.rows.forEach((row) => {
        const status = row.status === 1 ? '未超过2次' : '超过2次';
        // 使用北京时间 (UTC+8)
        const beijingTime = new Date(new Date(row.timestamp).getTime() + 8 * 60 * 60 * 1000).toLocaleString('zh-CN');
        const beijingCheckedTime = row.checked_at ? new Date(new Date(row.checked_at).getTime() + 8 * 60 * 60 * 1000).toLocaleString('zh-CN') : '';
        csvContent += `${row.id},${row.signature},${row.slot},${row.from_address},${row.from_label || ''},${row.to_address},${row.to_label || ''},${row.amount},${beijingTime},${status},${beijingCheckedTime}\n`;
      });
    }

    // 返回 CSV 文件
    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="wallet_transfers_${startDate}_${endDate}.csv"`,
      },
    });
  } catch (error) {
    console.error('导出交易记录失败:', error);
    return NextResponse.json(
      { error: '导出交易记录失败' },
      { status: 500 }
    );
  }
}
