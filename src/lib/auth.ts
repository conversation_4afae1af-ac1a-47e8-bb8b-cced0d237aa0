import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import pool from "@/lib/db";

export const authOptions: NextAuthOptions = {
  // 添加密钥配置，用于加密 JWT
  secret: process.env.NEXTAUTH_SECRET,

  // 配置 URL，使用相对路径，这样会自动适应当前域名
  useSecureCookies: false, // 如果不是 HTTPS，需要设置为 false
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: false // 如果不是 HTTPS，需要设置为 false
      }
    }
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "用户名", type: "text" },
        password: { label: "密码", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        try {
          // 查询用户
          const result = await pool.query(
            'SELECT * FROM users WHERE username = $1',
            [credentials.username]
          );

          const user = result.rows[0];

          if (!user) {
            return null;
          }

          // 验证密码
          const isValid = await bcrypt.compare(credentials.password, user.password_hash);

          if (!isValid) {
            return null;
          }

          // 更新最后登录时间
          await pool.query(
            'UPDATE users SET last_login = NOW() WHERE id = $1',
            [user.id]
          );

          return {
            id: user.id.toString(),
            name: user.username,
          };
        } catch (error) {
          console.error("认证错误:", error);
          return null;
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30天
  },
  pages: {
    signIn: "/login",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
      }
      return session;
    },
  },
};
