import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 检查是否是需要保护的路由
  const isProtectedRoute =
    pathname.startsWith('/dashboard') ||
    pathname.startsWith('/wallet');

  // 检查是否是认证路由
  const isAuthRoute = pathname.startsWith('/login');

  // 获取会话令牌，设置 secure: false 以支持非 HTTPS 环境
  const token = await getToken({
    req: request,
    secureCookie: false // 非 HTTPS 环境
  });

  console.log('当前路径:', pathname);
  console.log('认证状态:', token ? '已登录' : '未登录');

  // 如果没有令牌且访问的是受保护的路由，重定向到登录页面
  if (!token && isProtectedRoute) {
    console.log('未授权访问，重定向到登录页面');
    // 使用相对路径，避免域名问题
    const url = new URL('/login', request.url);
    return NextResponse.redirect(url);
  }

  // 如果有令牌且访问的是认证路由，重定向到仪表盘
  if (token && isAuthRoute) {
    console.log('已登录用户访问登录页，重定向到仪表盘');
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  return NextResponse.next();
}

// 配置匹配的路由
export const config = {
  matcher: ['/dashboard/:path*', '/wallet/:path*', '/login'],
};
