const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// 数据库连接配置
const pool = new Pool({
  user: 'postgres',
  password: '46e8FJ0t0rTgfk0wqc',
  host: '*************',
  port: 5432,
  database: 'postgres',
});

// 默认用户信息
const DEFAULT_USERNAME = 'admin';
const DEFAULT_PASSWORD = 'admin123';

async function setupDatabase() {
  console.log('开始设置数据库...');
  
  try {
    // 检查用户表是否存在
    const checkTableResult = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'users'
      );
    `);

    // 如果用户表不存在，创建它
    if (!checkTableResult.rows[0].exists) {
      console.log('创建 users 表...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS users (
          id SERIAL PRIMARY KEY,
          username VARCHAR(50) NOT NULL UNIQUE,
          password_hash VARCHAR(255) NOT NULL,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          last_login TIMESTAMPTZ
        );
      `);
      console.log('users 表创建成功');
    } else {
      console.log('users 表已存在');
    }

    // 检查用户是否已存在
    const checkUserResult = await pool.query(
      'SELECT * FROM users WHERE username = $1',
      [DEFAULT_USERNAME]
    );

    if (checkUserResult.rows.length > 0) {
      console.log(`用户 "${DEFAULT_USERNAME}" 已存在，无需创建`);
    } else {
      // 对密码进行哈希处理
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(DEFAULT_PASSWORD, salt);

      // 创建用户
      const result = await pool.query(
        'INSERT INTO users (username, password_hash) VALUES ($1, $2) RETURNING id, username, created_at',
        [DEFAULT_USERNAME, hashedPassword]
      );

      console.log('用户创建成功:', result.rows[0]);
    }

    console.log(`登录信息：用户名: ${DEFAULT_USERNAME}, 密码: ${DEFAULT_PASSWORD}`);
    console.log('数据库设置完成');
  } catch (error) {
    console.error('设置数据库失败:', error);
  } finally {
    // 关闭数据库连接
    await pool.end();
  }
}

// 执行设置
setupDatabase();
