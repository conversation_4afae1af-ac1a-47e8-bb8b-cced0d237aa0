import bcrypt from 'bcryptjs';
import { Pool } from 'pg';

// 数据库连接配置
const pool = new Pool({
  user: 'postgres',
  password: '46e8FJ0t0rTgfk0wqc',
  host: '*************',
  port: 5432,
  database: 'postgres',
});

console.log('脚本开始执行...');

// 默认用户信息
const DEFAULT_USERNAME = 'admin';
const DEFAULT_PASSWORD = 'admin123';

async function createUser(username: string, password: string) {
  try {
    // 检查用户表是否存在
    const checkTableResult = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'users'
      );
    `);

    // 如果用户表不存在，创建它
    if (!checkTableResult.rows[0].exists) {
      console.log('创建 users 表...');
      await pool.query(`
        CREATE TABLE IF NOT EXISTS users (
          id SERIAL PRIMARY KEY,
          username VARCHAR(50) NOT NULL UNIQUE,
          password_hash VARCHAR(255) NOT NULL,
          created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
          last_login TIMESTAMPTZ
        );
      `);
    }

    // 检查用户是否已存在
    const checkUserResult = await pool.query(
      'SELECT * FROM users WHERE username = $1',
      [username]
    );

    if (checkUserResult.rows.length > 0) {
      console.log(`用户 "${username}" 已存在，跳过创建`);
      return;
    }

    // 对密码进行哈希处理
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // 创建用户
    const result = await pool.query(
      'INSERT INTO users (username, password_hash) VALUES ($1, $2) RETURNING id, username, created_at',
      [username, hashedPassword]
    );

    console.log('用户创建成功:', result.rows[0]);
    console.log(`用户名: ${username}`);
    console.log(`密码: ${password}`);
  } catch (error) {
    console.error('创建用户失败:', error);
  } finally {
    // 关闭数据库连接
    await pool.end();
  }
}

// 从命令行参数获取用户名和密码，如果没有提供则使用默认值
const username = process.argv[2] || DEFAULT_USERNAME;
const password = process.argv[3] || DEFAULT_PASSWORD;

console.log(`正在创建用户 "${username}"...`);
createUser(username, password);
